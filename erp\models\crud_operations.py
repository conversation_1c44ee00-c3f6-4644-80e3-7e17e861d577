"""
CRUD operations mixin for models
"""
import time
from datetime import datetime
from typing import Dict, Any, List

from ..database.registry import DatabaseRegistry
from ..utils import DomainFilter
from ..logging import get_logger
from ..context.manager import ContextManager


class CRUDOperationsMixin:
    """Mixin providing CRUD operations for models"""

    @classmethod
    async def create(cls, vals: Dict[str, Any]):
        """Create a new record in database"""
        logger = get_logger(f"{__name__}.{cls._name or cls.__name__}")
        start_time = time.perf_counter()

        logger.debug(f"Creating new {cls._name} record")

        record = cls(**vals)

        # Set create_uid to current user if not already set
        if 'create_uid' not in vals:
            current_user_id = ContextManager.get_user()
            if current_user_id:
                record._values['create_uid'] = str(current_user_id)

        # Get database manager
        db = await DatabaseRegistry.get_current_database()
        if not db:
            logger.error(f"No database connection available for {cls._name} create")
            raise RuntimeError("No database connection available")

        # Prepare data for insertion
        insert_data = {}
        relational_data = {}

        for field_name, value in record._values.items():
            if field_name in cls._fields:
                field = cls._fields[field_name]
                
                # Handle relational fields separately
                if hasattr(field, 'comodel_name'):
                    relational_data[field_name] = value
                else:
                    insert_data[field_name] = value

        # Insert into database
        table_name = cls._table or cls._name.replace('.', '_')
        logger.debug(f"Inserting into table: {table_name}")

        # Convert field names to database column names
        db_insert_data = cls._convert_data_to_db_columns(insert_data)
        record_id = await db.insert(table_name, db_insert_data)

        if record_id:
            record._values['id'] = str(record_id)
            logger.debug(f"Record created with ID: {record_id}")
        else:
            logger.warning(f"No ID returned from insert for {cls._name}")

        record._is_new_record = False

        # Process relational field commands
        if relational_data:
            await record._process_relational_commands(relational_data, db)

        duration = time.perf_counter() - start_time
        logger.debug(f"Created {cls._name} record in {duration:.3f}s")

        return record

    async def write(self, vals: Dict[str, Any]):
        """Update record with new values"""
        start_time = time.perf_counter()
        record_id = self._values.get('id')

        self._logger.debug(f"Updating {self._name} record {record_id}")

        if self._is_new_record:
            self._logger.error(f"Cannot update a new {self._name} record. Use create() instead.")
            raise ValueError("Cannot update a new record. Use create() instead.")

        # Update values
        for field_name, value in vals.items():
            if field_name in self._fields:
                setattr(self, field_name, value)

        # Update updateAt timestamp
        if 'updateAt' in self._fields:
            self._values['updateAt'] = datetime.now()

        # Set update_uid to current user if not already set in vals
        if 'update_uid' not in vals and 'update_uid' in self._fields:
            current_user_id = ContextManager.get_user()
            if current_user_id:
                self._values['update_uid'] = str(current_user_id)

        # Save to database
        db = await DatabaseRegistry.get_current_database()
        if not db:
            self._logger.error(f"No database connection available for {self._name} update")
            raise RuntimeError("No database connection available")

        table_name = self._table or self._name.replace('.', '_')

        if not record_id:
            self._logger.error(f"Record has no ID for update in {self._name}")
            raise ValueError("Record has no ID for update")

        # Prepare update data
        update_data = {}
        for field_name, value in vals.items():
            if field_name in self._fields and not hasattr(self._fields[field_name], 'comodel_name'):
                update_data[field_name] = value

        # Convert field names to database column names
        db_update_data = self._convert_data_to_db_columns(update_data)
        
        await db.update(table_name, record_id, db_update_data)

        duration = time.perf_counter() - start_time
        self._logger.debug(f"Updated {self._name} record {record_id} in {duration:.3f}s")

        return True

    async def unlink(self):
        """Delete record from database"""
        if self._is_new_record:
            raise ValueError("Cannot delete a new record")
        
        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        table_name = self._table or self._name.replace('.', '_')
        record_id = self._values.get('id')
        
        if not record_id:
            raise ValueError("Record has no ID for deletion")
        
        await db.delete(table_name, record_id)
        return True

    @classmethod
    async def search(cls, domain: List = None, offset: int = 0, limit: int = None, 
                    order: str = None, count: bool = False):
        """Search for records matching domain"""
        logger = get_logger(f"{__name__}.{cls._name or cls.__name__}")
        start_time = time.perf_counter()

        domain = domain or []
        logger.debug(f"Searching {cls._name} with domain: {domain}")

        # Get database manager
        db = await DatabaseRegistry.get_current_database()
        if not db:
            logger.error(f"No database connection available for {cls._name} search")
            raise RuntimeError("No database connection available")

        table_name = cls._table or cls._name.replace('.', '_')

        # Convert domain to SQL WHERE clause
        domain_filter = DomainFilter(domain)
        where_clause, params = domain_filter.to_sql()

        if count:
            # Return count only
            result = await db.count(table_name, where_clause, params)
            duration = time.perf_counter() - start_time
            logger.debug(f"Counted {cls._name} records in {duration:.3f}s")
            return result

        # Search for records
        records_data = await db.search(
            table_name, 
            where_clause=where_clause, 
            params=params,
            offset=offset, 
            limit=limit, 
            order=order
        )

        # Convert to model instances
        from .recordset import RecordSet
        records = []
        for record_data in records_data:
            record = cls()
            record._values.update(record_data)
            record._is_new_record = False
            records.append(record)

        duration = time.perf_counter() - start_time
        logger.debug(f"Found {len(records)} {cls._name} records in {duration:.3f}s")

        return RecordSet(cls, records)

    @classmethod
    async def browse(cls, ids: List[str]):
        """Browse records by IDs"""
        if not ids:
            from .recordset import RecordSet
            return RecordSet(cls, [])

        domain = [('id', 'in', ids)]
        return await cls.search(domain)

    async def _process_relational_commands(self, relational_data: Dict[str, Any], db):
        """Process relational field commands"""
        # TODO: Implement relational field command processing for One2Many and Many2Many
        # Suppress unused parameter warnings for now
        _ = relational_data, db
